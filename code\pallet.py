from box import Box
from dataclasses import dataclass

@dataclass
class Pallet:
    def __init__(self, width, depth, height, max_height, packed_boxes: list[Box] = None):
        self.width = width
        self.depth = depth
        self.height = height
        self.max_height = max_height
        self.dimensions = (self.width, self.depth, self.height)
        self.packed_boxes = [] if packed_boxes is None else packed_boxes
        self.volume = self.width*self.depth*self.max_height


    def clear(self):
        self.packed_boxes = []
