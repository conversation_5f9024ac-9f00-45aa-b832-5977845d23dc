import pybullet as p
import pybullet_data
from box import Box
from pallet import Pallet
import random
from dataclasses import dataclass

@dataclass
class BulletSim:

    pallet: Pallet
    mass : int = 5

    def __init__(self, pallet: Pallet):
        self.pallet = pallet

        # Start PyBullet-session  
        physicsClient = p.connect(p.DIRECT) # p.DIRECT or p.GUI

        # Speed up somulation
        p.setRealTimeSimulation(0)
        p.setTimeStep(1./120.)
        
        # Initialise PyBullet
        p.setAdditionalSearchPath(pybullet_data.getDataPath())
        p.setGravity(0, 0, -5)
        

        # Initialise Vieuw
        cameraDistance = 2
        cameraYaw = 50  
        cameraPitch = -35  
        cameraTargetPosition = [self.pallet.width, self.pallet.depth, 1] 
        p.resetDebugVisualizerCamera(cameraDistance, cameraYaw, cameraPitch, cameraTargetPosition)

        # Baseplane
        planeId = p.loadURDF("plane.urdf")

        # Initialise the pallet
        palletHalfExtents = [dim/2 for dim in pallet.dimensions]
        startPos_pallet = [palletHalfExtents[0], palletHalfExtents[1], -palletHalfExtents[2]]
        startOrientation_pallet = p.getQuaternionFromEuler([0, 0, 0])
        collisionShapeId_pallet = p.createCollisionShape(p.GEOM_BOX, halfExtents = palletHalfExtents)
        visualShapeId_pallet = p.createVisualShape(p.GEOM_BOX, halfExtents=palletHalfExtents, rgbaColor=[0.647, 0.165, 0.165, 1])  # Pallet has brown colour
        palletId = p.createMultiBody(baseMass=0, baseCollisionShapeIndex=collisionShapeId_pallet, baseVisualShapeIndex=visualShapeId_pallet,
                                basePosition=startPos_pallet, baseOrientation=startOrientation_pallet)

        # Initialise invisible walls (borders of pallet)
        startPos_wall1 = [-0.10001, palletHalfExtents[1], 0] 
        startOrientation_wall1 = p.getQuaternionFromEuler([0, 0, 0])
        wall1HalfExtents = [0.1, palletHalfExtents[1]*2, 2*pallet.max_height]
        collisionShapeId_wall1 = p.createCollisionShape(p.GEOM_BOX, halfExtents = wall1HalfExtents)
        visualShapeId_wall1 = p.createVisualShape(p.GEOM_BOX, halfExtents=wall1HalfExtents, rgbaColor=[0, 0, 0, 0])  # Invisible wall
        wall1Id = p.createMultiBody(baseMass=0, baseCollisionShapeIndex=collisionShapeId_wall1, baseVisualShapeIndex=visualShapeId_wall1,
                                basePosition=startPos_wall1, baseOrientation=startOrientation_wall1)

        startPos_wall2 = [2*palletHalfExtents[0]+0.10001, palletHalfExtents[1], 0]
        startOrientation_wall2 = p.getQuaternionFromEuler([0, 0, 0])
        wall2HalfExtents = [0.1, palletHalfExtents[1]*2, 2*pallet.max_height]
        collisionShapeId_wall2 = p.createCollisionShape(p.GEOM_BOX, halfExtents = wall2HalfExtents)
        visualShapeId_wall2 = p.createVisualShape(p.GEOM_BOX, halfExtents=wall2HalfExtents, rgbaColor=[0, 0, 0, 0])  # Invisible wall
        wall2Id = p.createMultiBody(baseMass=0, baseCollisionShapeIndex=collisionShapeId_wall2, baseVisualShapeIndex=visualShapeId_wall2,
                                basePosition=startPos_wall2, baseOrientation=startOrientation_wall2)
        
        startPos_wall3 = [palletHalfExtents[0], 2*palletHalfExtents[1]+0.10001, 0]
        startOrientation_wall3 = p.getQuaternionFromEuler([0, 0, 0])
        wall3HalfExtents = [palletHalfExtents[0]*2, 0.1, 2*pallet.max_height]
        collisionShapeId_wall3 = p.createCollisionShape(p.GEOM_BOX, halfExtents = wall3HalfExtents)
        visualShapeId_wall3 = p.createVisualShape(p.GEOM_BOX, halfExtents=wall3HalfExtents, rgbaColor=[0, 0, 0, 0])  # Invisible wall
        wall3Id = p.createMultiBody(baseMass=0, baseCollisionShapeIndex=collisionShapeId_wall3, baseVisualShapeIndex=visualShapeId_wall3,
                                basePosition=startPos_wall3, baseOrientation=startOrientation_wall3)

        startPos_wall4 = [palletHalfExtents[0], -0.10001, 0]
        startOrientation_wall4 = p.getQuaternionFromEuler([0, 0, 0])
        wall4HalfExtents = [palletHalfExtents[0]*2, 0.1, 2*pallet.max_height]
        collisionShapeId_wall4 = p.createCollisionShape(p.GEOM_BOX, halfExtents = wall4HalfExtents)
        visualShapeId_wall4 = p.createVisualShape(p.GEOM_BOX, halfExtents=wall4HalfExtents, rgbaColor=[0, 0, 0, 0])  # Invisible wall
        wall4Id = p.createMultiBody(baseMass=0, baseCollisionShapeIndex=collisionShapeId_wall4, baseVisualShapeIndex=visualShapeId_wall4,
                                basePosition=startPos_wall4, baseOrientation=startOrientation_wall4)
        

    def simulate_box(self, box: Box, location):

        box.x, box.y, box.z = location[0], location[1], 1.5*self.pallet.max_height # Drop from high

        # Initialise box
        startPosbox = [box.x+box.width/2, box.y+box.depth/2, box.z]
        startOrientationbox = p.getQuaternionFromEuler([box.roll, box.pitch, box.yaw])
        boxHalfExtents = [box.width/2, box.depth/2, box.height/2] 

        # Create box
        collisionShapeId_box = p.createCollisionShape(p.GEOM_BOX, halfExtents=boxHalfExtents)
        r = random.uniform(0, 1) # Randomising the colour of the box 
        g = random.uniform(0, 1)  
        b = random.uniform(0, 1)  
        visualShapeId_box = p.createVisualShape(p.GEOM_BOX, halfExtents=boxHalfExtents, rgbaColor=[r, g, b, 1]) 
        boxId = p.createMultiBody(baseMass=(self.mass), baseCollisionShapeIndex=collisionShapeId_box, baseVisualShapeIndex=visualShapeId_box, 
                                basePosition=startPosbox, baseOrientation=startOrientationbox)

        # Start simulation
        for i in range(150):
            p.stepSimulation()

            # Check if the box is stabilized (using velocity threshold as an indicator)
            lin_vel, ang_vel = p.getBaseVelocity(boxId)
            if all(abs(v) < 0.01 for v in lin_vel) and all(abs(v) < 0.01 for v in ang_vel):
                break  # Exit loop early if the box has settled

    def place_box(self, box: Box):

        # Initialise box
        startPosbox = [box.x+box.width/2, box.y+box.depth/2, box.z+box.height/2]
        startOrientationbox = p.getQuaternionFromEuler([box.roll, box.pitch, box.yaw])
        boxHalfExtents = [box.width/2, box.depth/2, box.height/2] 

        # Create box
        collisionShapeId_box = p.createCollisionShape(p.GEOM_BOX, halfExtents=boxHalfExtents)
        r = random.uniform(0, 1) # Randomising the colour of the box 
        g = random.uniform(0, 1)  
        b = random.uniform(0, 1)  
        visualShapeId_box = p.createVisualShape(p.GEOM_BOX, halfExtents=boxHalfExtents, rgbaColor=[r, g, b, 1]) 
        boxId = p.createMultiBody(baseMass=(0), baseCollisionShapeIndex=collisionShapeId_box, baseVisualShapeIndex=visualShapeId_box, 
                                basePosition=startPosbox, baseOrientation=startOrientationbox)

        # Start simulation
        for i in range(175):
            p.stepSimulation()

            # Check if the box is stabilized (using velocity threshold as an indicator)
            lin_vel, ang_vel = p.getBaseVelocity(boxId)
            if all(abs(v) < 0.01 for v in lin_vel) and all(abs(v) < 0.01 for v in ang_vel):
                break  # Exit loop early if the box has settled
