import os
import time
import open3d as o3d
import numpy as np
import cv2

def plot_pallet(pallet):
    # Create the pallet mesh centered at the origin
    pallet_mesh = o3d.geometry.TriangleMesh.create_box(pallet.width, pallet.height, pallet.depth)
    pallet_mesh.paint_uniform_color([0.7, 0.7, 0.7])  # Light grey for the pallet

    # Translate so that the top-center corner is at (0, 0, 0)
    translation = np.array([
        0,
        -pallet.height,
        0
    ])
    pallet_mesh.translate(translation)
    return pallet_mesh

def plot_box(box, color):
    # Create the box mesh centered at the origin
    box_mesh = o3d.geometry.TriangleMesh.create_box(box.width, box.height, box.depth)
    box_mesh.paint_uniform_color(color)

    # Define the rotation angles in radians
    roll = -box.roll
    yaw = -box.pitch
    pitch = -box.yaw

    # Compute rotation matrices for roll, pitch, and yaw
    R_roll = np.array([
        [1, 0, 0],
        [0, np.cos(roll), -np.sin(roll)],
        [0, np.sin(roll), np.cos(roll)]
    ])

    R_pitch = np.array([
        [np.cos(pitch), 0, -np.sin(pitch)],
        [0, 1, 0],
        [np.sin(pitch), 0, np.cos(pitch)]
    ])

    R_yaw = np.array([
        [np.cos(yaw), -np.sin(yaw), 0],
        [np.sin(yaw), np.cos(yaw), 0],
        [0, 0, 1]
    ])

    # Combined rotation matrix: R = R_yaw * R_pitch * R_roll
    R = R_yaw @ R_pitch @ R_roll

    # Apply rotation to the box mesh

    # Translate so that the bottom-left corner is at the desired position
    translation = np.array([
        box.position[0],
        box.position[2],
        box.position[1]
    ])

    # Compute the center of the box
    box_center = np.array([box.width / 2, box.height / 2, box.depth / 2])

    # Translate the mesh to have its center at the origin before rotation
    box_mesh.translate(-box_center)

    box_mesh.rotate(R, center=(0,0,0))

    box_mesh.translate(box_center)

    box_mesh.translate(translation)

    return box_mesh

def random_color():
    # Generate a random color
    return np.random.rand(3).tolist()  # Return a list of 3 random values between 0 and 1

def visualise_o3d_steps(packer):
    # Create a list to store all geometries (pallet + boxes)
    geometries = []

    # Plot the pallet
    pallet_mesh = plot_pallet(packer.pallet)
    geometries.append(pallet_mesh)

    # Plot each packed box on the pallet with a random color
    for box in packer.pallet.packed_boxes:
        color = random_color()  # Generate a random color
        box_mesh = plot_box(box, color)
        geometries.append(box_mesh)
        o3d.visualization.draw_geometries(geometries)

    # Visualize all geometries together

def visualise_o3d(packer):
            # Create a list to store all geometries (pallet + boxes)
    geometries = []

    # Plot the pallet
    pallet_mesh = plot_pallet(packer.pallet)
    geometries.append(pallet_mesh)

    # Plot each packed box on the pallet with a random color
    for i in range(len(packer.pallet.packed_boxes)):
        box = packer.pallet.packed_boxes[i]
        color = random_color()  # Generate a random color
        box_mesh = plot_box(box, color)
        geometries.append(box_mesh)
    o3d.visualization.draw_geometries(geometries)
