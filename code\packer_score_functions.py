import math
import numpy as np

def calculate_score_gaps(pallet, p, i, box, distance_between = 0.03, walls=True):

    pos, orn = p.getBasePositionAndOrientation(i)  # This will get the center of the box in pos
    start_point = pos[0], pos[1], pos[2]
    euler = p.getEulerFromQuaternion(orn)
    x, y, z = euler[0], euler[1], euler[2]

    end_points = [[x + 10, y, z], [x - 10, y, z], [x, y + 10, z], [x, y - 10, z]] # 10 is how far to check for other boxes
    aabb_min, aabb_max = p.getAABB(i)
    width = aabb_max[0] - aabb_min[0]
    depth = aabb_max[1] - aabb_min[1]

    distances = []

    # Mesure distances to closest object in pos and neg x and y direction
    for end_point in end_points:
        ray_info = p.rayTest(start_point, end_point) # Perform ray test
        if ray_info[0][0] != -1:  # -1 means no hit
            hit_position = ray_info[0][3] # ray_info[0][3] gives the hit position if any object was hit
            hit_position = np.array(hit_position).flatten() 
            distance = np.linalg.norm(np.array(start_point) - np.array(hit_position))
            distances.append(distance)
        else:
            distances.append(0) 

    distances = round(distances[0]-width/2, 2), round(distances[1]-width/2, 2), round(distances[2]-depth/2, 2), round(distances[3]-depth/2, 2)
    score = 0

  #0.0005 to have slight room for errors
    for dist in distances:
        if abs(dist) < distance_between:
            return (-100)
        elif abs(abs(dist) - distance_between) < 0.005:
            score += 2

        elif walls:
            score += (1/(dist+1))**2 + (dist/2)**2 # Very small en very large distances are most wanted
    
    # if walls:
    #     if abs(pos[0] + box.width/2 -pallet.width) < 0.0005 or abs(pos[0] - box.width/2) <0.0005:
    #         score += 4
    #     if abs(pos[1] + box.depth/2 - pallet.depth) < 0.0005 or abs(pos[1] - box.depth/2) < 0.0005:
    #         score += 4
    # else:
    #     if abs(pos[0] + box.width/2 -pallet.width) < 0.0005 or abs(pos[0] - box.width/2) <0.0005:
    #         score += 0
    #     if abs(pos[1] + box.depth/2 - pallet.depth) < 0.0005 or abs(pos[1] - box.depth/2) < 0.0005:
    #         score += 0

    

    return score

def calculate_score_height(pallet, p, i):
    
    # Get highest point
    highest_point = p.getAABB(i)[1][2]

    score = (pallet.max_height-highest_point)
    return score

def calculate_score_volume(box):

    score = box.width + box.depth + box.height
    return score

def calculate_score_skewed(p, i):

    pos, orn = p.getBasePositionAndOrientation(i)
    euler = p.getEulerFromQuaternion(orn)

    rad_roll = abs(abs(euler[0]) - round(abs(euler[0]) / (math.pi/2)) * math.pi/2)
    rad_pitch = abs(abs(euler[1]) - round(abs(euler[1]) / (math.pi/2)) * math.pi/2)
    rad_yaw = abs(abs(euler[2]) - round(abs(euler[2]) / (math.pi/2)) * math.pi/2)
    
    # Check if any rotation exceeds the maximum skewness threshold

    score = 1/(rad_roll + rad_pitch + rad_yaw + 1)
    score = (score**3)
    return score


